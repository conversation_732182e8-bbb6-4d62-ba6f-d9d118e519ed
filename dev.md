
<https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-access-token/getAccessToken.html>
<https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html>
<https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html>

服务号开发相关
获取token
<https://developers.weixin.qq.com/doc/service/api/base/api_getaccesstoken.html>

 <https://www.paddlepaddle.org.cn/install/quick?docurl=/documentation/docs/zh/develop/install/pip/windows-pip.html>

 python -m pip install paddlepaddle==3.1.1 -i <https://www.paddlepaddle.org.cn/packages/stable/cpu/>

用户 id

员工 id user_id
会员 id user_id

Alembic

开发一个使用Java进行开发的Android应用程序，采用以下技术架构和功能实现方案：

技术架构：

1. 网络请求层：
   - 使用OkHttp作为底层HTTP客户端
   - 使用Retrofit2进行REST API封装
2. 数据持久层：
   - 使用SQLite实现本地数据缓存
3.图片处理：
 CameraX + Glide/Picasso  
4. 架构模式：
   - 采用MVVM设计模式进行架构设计
5.权限处理：
   -Activity Result API（Android 11+ 推荐方式

核心功能实现要求：

1. 数据获取功能：
   - 实现分页加载的数据列表获取
   - 支持下拉刷新和上拉加载更多
   - 网络数据与本地缓存同步机制

2. 数据上传功能：
   - 实现表单数据提交
   - 支持多文件上传
   - 包含上传进度显示

3. 多媒体功能：
   - 相机调用实现：
     - 完整处理相机权限申请流程
     - 支持图片拍摄和选择
   - 图片上传：
     - 实现图片压缩处理
     - 支持多图上传
     - 附带表单信息提交

4. 页面功能：
   - 列表页：
     - 展示数据列表
     - 支持点击跳转详情
     - 包含搜索和筛选功能
   - 详情页：
     - 展示完整数据详情
     - 支持相关操作（如收藏、分享等）
  
  要实现的页面有，登录页，splash页，首页，个人中心，聊天页，好友信息页，类似朋友圈页，商品列表页，商品详情页，资讯列表页，资讯详情页
  
Activity 生命周期处理

    Service例子
 BroadcastReceiver例子
 SharedPreferences使用
 EventBus使用
 ViewModel和LiveData使用
 fragment例子
 
优化要求：

1. 性能优化：
   - 实现图片懒加载
   - 优化SQLite查询效率
2. 用户体验：
   - 添加加载状态提示
   - 实现网络错误处理
   - 支持离线模式
3. 代码规范：
   - 遵循Clean Architecture原则
   - 合理划分模块职责
   - 编写清晰的代码注释
